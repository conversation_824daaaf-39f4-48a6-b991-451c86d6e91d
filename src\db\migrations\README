# Alembic Database Migrations

This directory contains Alembic migration files for the Restaurant Menu API database.

## Prerequisites

1. Make sure PostgreSQL is running (Docker: `docker-compose up -d`)
2. Make sure your virtual environment is activated
3. Make sure your .env file has the correct DATABASE_URL

## Basic Alembic Commands

### Create a new migration (auto-generate from model changes):
```bash
alembic revision --autogenerate -m "description of changes"
```

### Apply all pending migrations:
```bash
alembic upgrade head
```

### Downgrade one migration:
```bash
alembic downgrade -1
```

### Check current migration version:
```bash
alembic current
```

### Show migration history:
```bash
alembic history
```

### Show pending migrations:
```bash
alembic show head
```

## Initial Setup

If this is a fresh database, you'll need to:

1. Create your first migration:
   ```bash
   alembic revision --autogenerate -m "initial migration"
   ```

2. Apply the migration:
   ```bash
   alembic upgrade head
   ```

## Notes

- Always review auto-generated migrations before applying them
- Test both upgrade and downgrade operations
- The database URL is automatically loaded from your .env file
