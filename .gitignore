# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
htmlcov/
.coverage
coverage.xml
*.cover

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime-project
*.sublime-workspace

# Database
*.db
*.sqlite
*.sqlite3
*.sql
*.bak
postgres-data/

# Project specific
output/
stores_database.json
*.log
chromedriver
chromedriver.exe

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Temporary and generated files
garbage/
temp/
tmp/
.tmp/
*.tmp

# JSON files
pizza_hut_combined*.json
dummy.json
chipotle_*.json
pizzahut_*.json
*_data.json

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Alembic
# Keep the migrations directory but ignore version files
# db/migrations/versions/*
# !db/migrations/versions/.gitkeep