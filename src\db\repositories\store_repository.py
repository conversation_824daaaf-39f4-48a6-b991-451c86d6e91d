from sqlalchemy.orm import Session
from typing import List, Optional
from db.models import Store
from db.schemas import StoreCreate

class StoreRepository:
    @staticmethod
    def get_all(db: Session, skip: int = 0, limit: int = 100) -> List[Store]:
        return db.query(Store).offset(skip).limit(limit).all()

    @staticmethod
    def get_by_id(db: Session, store_id: int) -> Optional[Store]:
        return db.query(Store).filter(Store.id == store_id).first()

    @staticmethod
    def get_by_restaurant_number(db: Session, restaurant_number: str) -> Optional[Store]:
        return db.query(Store).filter(Store.restaurant_number == restaurant_number).first()

    @staticmethod
    def get_by_restaurant(db: Session, restaurant: str) -> List[Store]:
        return db.query(Store).filter(Store.restaurant == restaurant).all()

    @staticmethod
    def get_by_restaurant_and_zipcode(db: Session, restaurant: str, zipcode: str) -> List[Store]:
        """
        Get stores by restaurant and zipcode

        First tries to find stores where store_code matches the zipcode.
        If none found, tries to find stores where searched_code matches the zipcode.

        Args:
            db: Database session
            restaurant: Restaurant name
            zipcode: Zipcode to search for

        Returns:
            List of stores sorted by sequence
        """
        # First try to find by store_code (store's actual code)
        stores = db.query(Store).filter(
            Store.restaurant == restaurant,
            Store.store_code == zipcode
        ).order_by(Store.sequence).all()

        # If no stores found by store_code, try searched_code
        if not stores:
            stores = db.query(Store).filter(
                Store.restaurant == restaurant,
                Store.searched_code == zipcode
            ).order_by(Store.sequence).all()

        return stores

    @staticmethod
    def get_by_restaurant_and_restaurant_number(db: Session, restaurant: str, restaurant_number: str) -> Optional[Store]:
        return db.query(Store).filter(Store.restaurant == restaurant, Store.restaurant_number == restaurant_number).first()

    @staticmethod
    def get_by_restaurant_and_store_code(db: Session, restaurant: str, store_code: str) -> Optional[Store]:
        """Get a store by restaurant and store_code (store's actual code)"""
        return db.query(Store).filter(Store.restaurant == restaurant, Store.store_code == store_code).first()

    @staticmethod
    def get_by_zipcode(db: Session, zipcode: str) -> List[Store]:
        """
        Get stores by zipcode

        First tries to find stores where store_code matches the zipcode.
        If none found, tries to find stores where searched_code matches the zipcode.

        Args:
            db: Database session
            zipcode: Zipcode to search for

        Returns:
            List of stores sorted by sequence
        """
        # First try to find by store_code (store's actual code)
        stores = db.query(Store).filter(Store.store_code == zipcode).order_by(Store.sequence).all()

        # If no stores found by store_code, try searched_code
        if not stores:
            stores = db.query(Store).filter(Store.searched_code == zipcode).order_by(Store.sequence).all()

        return stores

    @staticmethod
    def create(db: Session, store: StoreCreate) -> Store:
        # Use model_dump instead of dict (which is deprecated in Pydantic v2)
        db_store = Store(**store.model_dump())
        db.add(db_store)
        db.commit()
        db.refresh(db_store)
        return db_store

    @staticmethod
    def update(db: Session, store_id: int, store_data: dict) -> Optional[Store]:
        db_store = db.query(Store).filter(Store.id == store_id).first()
        if db_store:
            for key, value in store_data.items():
                setattr(db_store, key, value)
            db.commit()
            db.refresh(db_store)
        return db_store

    @staticmethod
    def delete(db: Session, store_id: int) -> bool:
        db_store = db.query(Store).filter(Store.id == store_id).first()
        if db_store:
            db.delete(db_store)
            db.commit()
            return True
        return False
