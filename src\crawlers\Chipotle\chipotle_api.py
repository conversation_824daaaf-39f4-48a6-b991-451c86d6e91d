import requests
import json
import os
import sys
import argpar<PERSON>
from typing import Dict, Any, List, Optional, Tuple
from geopy.geocoders import Nominatim
from .const import (
    RESTAURANT_API_URL,
    MENU_API_URL_TEMPLATE,
    RESTAURANT_HEADERS,
    MENU_HEADERS,
    METADATA_HEADERS,
    METADATA_API_URL,
    STORE_FILE_TEMPLATES,
    COMBINED_FILE_TEMPLATE,
    STORE_TABLE_HEADERS,
    MODE_TEST,
    MODE_SHOW,
    MODE_DEFAULT,
    TEST_COMBINED_FILE_TEMPLATE,
    MENU_SECTIONS
)

# Add parent directory to path to import db_utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import save_chipotle_data_to_db


class ChipotleAPI:
    """Chipotle API client for fetching store and menu data"""

    def __init__(self, headless: bool = True):
        """
        Initialize the Chipotle API client

        Args:
            headless: Not used in API implementation, but kept for compatibility
        """
        # This parameter is not used in the API implementation
        # but kept for compatibility with the crawler interface
        self.headless = headless

    def get_lat_long_from_zipcode(self, zipcode: str) -> Tuple[Optional[float], Optional[float]]:
        """
        Convert a zipcode to latitude and longitude using Nominatim geocoder.

        Args:
            zipcode: Zip code to convert

        Returns:
            Tuple of latitude and longitude or (None, None) if conversion failed
        """
        try:
            # Initialize the geocoder with increased timeout
            geolocator = Nominatim(user_agent="chipotle_finder", timeout=20)

            # Get location data for the zipcode
            location = geolocator.geocode(f"{zipcode}, USA")

            if location:
                print(f"Converted zipcode {zipcode} to coordinates")
                return location.latitude, location.longitude
            else:
                print(f"Could not find coordinates for zipcode {zipcode}")
                return None, None
        except Exception as e:
            print(f"Error converting zipcode to coordinates: {e}")
            return None, None

    def fetch_stores(self, zipcode: str) -> List[Dict[str, Any]]:
        """
        Fetch Chipotle stores for the given zipcode

        Args:
            zipcode: Zip code to search for stores

        Returns:
            List of store data dictionaries
        """
        # Try to load existing store data first
        existing_files = [template.format(zipcode=zipcode) for template in STORE_FILE_TEMPLATES]

        for file_path in existing_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"📋 Loaded existing store data from {file_path}")
                    return data
                except:
                    print(f"⚠️ Could not load existing store data from {file_path}")

        print(f"⚠️ No existing store data found, fetching fresh data...")

        # Convert zipcode to lat/long
        latitude, longitude = self.get_lat_long_from_zipcode(zipcode)

        if not latitude or not longitude:
            print("❌ Failed to get coordinates for zipcode. Exiting.")
            return []

        # Create payload with coordinates
        payload = {
            "latitude": latitude,
            "longitude": longitude,
            "radius": 80647,  # About 50 miles in meters
            "restaurantStatuses": ["OPEN", "LAB"],
            "conceptIds": ["CMG"],
            "orderBy": "distance",
            "orderByDescending": False,
            "pageSize": 10,
            "pageIndex": 0,
            "embeds": {
                "addressTypes": ["MAIN"],
                "realHours": True,
                "directions": True,
                "catering": True,
                "onlineOrdering": True,
                "timezone": True,
                "marketing": True,
                "chipotlane": True,
                "sustainability": True,
                "experience": True
            }
        }

        try:
            print(f"🔍 Searching for Chipotle stores with zipcode: {zipcode}...")
            response = requests.post(
                url=RESTAURANT_API_URL,
                headers=RESTAURANT_HEADERS,
                json=payload
            )

            response.raise_for_status()
            data = response.json()

            # Transform the data to a more usable format
            stores = []
            for restaurant in data.get('data', []):
                # Get the first address if available
                address = restaurant.get('addresses', [{}])[0] if restaurant.get('addresses') else {}

                # Create a simplified store object
                # Handle distance which could be a float or an object with a value property
                distance = restaurant.get('distance', 0)
                if isinstance(distance, dict):
                    distance_value = distance.get('value', 0)
                else:
                    distance_value = distance

                store = {
                    "restaurant_number": restaurant.get('restaurantNumber'),
                    "restaurant_name": address.get('addressLine1', restaurant.get('restaurantName', '')),
                    "distance": distance_value,
                    "postal_code": address.get('postalCode', ''),
                    "administrative_area": address.get('administrativeArea', ''),
                    "city": address.get('locality', ''),
                    "state": address.get('administrativeArea', ''),
                    "address1": address.get('addressLine1', ''),
                    "latitude": address.get('latitude'),
                    "longitude": address.get('longitude')
                }
                stores.append(store)

            # Sort stores by distance
            stores = sorted(stores, key=lambda x: float(x.get("distance", 999999)))
            print(f"📦 Found {len(stores)} stores, sorted by distance")
            return stores

        except requests.exceptions.RequestException as err:
            print(f"❌ Request failed: {err}")
            return []
        except json.JSONDecodeError as err:
            print(f"❌ JSON parsing failed: {err}")
            return []

    def fetch_menu_metadata(self) -> Dict[str, Any]:
        """
        Fetch menu metadata from the Chipotle API to get thumbnail URLs.

        Returns:
            Dictionary mapping item IDs to their metadata
        """
        print("🔍 Fetching menu metadata for thumbnails...")

        # Query parameters
        params = {
            "channel": "web",
            "region": "US"
        }

        try:
            response = requests.get(
                url=METADATA_API_URL,
                headers=METADATA_HEADERS,
                params=params
            )

            response.raise_for_status()
            data = response.json()

            # Create a dictionary to store item thumbnails
            thumbnail_map = {}

            # First check if we have items in the data
            if "items" in data and isinstance(data["items"], dict):
                for item_id, item_data in data["items"].items():
                    if "thumbnailUrl" in item_data:
                        thumbnail_map[item_id] = {
                            "thumbnailUrl": item_data["thumbnailUrl"]
                        }

                        # Add description if available
                        if "description" in item_data:
                            thumbnail_map[item_id]["description"] = item_data["description"]

            # If no items found in the expected format, try to extract from groups
            elif "groups" in data:
                # Process each group to find items with thumbnailImageUrl
                for group in data["groups"]:
                    # Get group thumbnail for reference
                    group_thumbnail = group.get("thumbnailImageUrl", "")

                    # Process items in this group
                    if "items" in group and isinstance(group["items"], list):
                        for item_entry in group["items"]:
                            if "menuItemId" in item_entry:
                                item_id = item_entry["menuItemId"]
                                # Only add if not already in the thumbnail map
                                if item_id not in thumbnail_map:
                                    thumbnail_map[item_id] = {}

                                    # For now, use the group's thumbnail URL as a fallback
                                    if group_thumbnail:
                                        thumbnail_map[item_id]["thumbnailUrl"] = group_thumbnail

            print(f"📸 Successfully fetched metadata for {len(thumbnail_map)} menu items")
            return thumbnail_map

        except requests.exceptions.RequestException as e:
            print(f"❌ Error making request for menu metadata: {e}")
            return {}
        except json.JSONDecodeError:
            print("❌ Error: Could not parse the menu metadata response as JSON")
            return {}

    def fetch_menu(self, restaurant_number: str) -> Optional[Dict[str, Any]]:
        """
        Fetch menu data for the selected store

        Args:
            restaurant_number: Restaurant number to fetch menu for

        Returns:
            Menu data dictionary or None if failed
        """
        url = MENU_API_URL_TEMPLATE.format(restaurant_number=restaurant_number)

        # Query parameters
        params = {
            "channelId": "web",
            "includeUnavailableItems": "true"
        }

        try:
            print(f"🔍 Fetching menu for restaurant #{restaurant_number}...")
            response = requests.get(url, headers=MENU_HEADERS, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as err:
            print(f"❌ Request failed: {err}")
            return None
        except json.JSONDecodeError:
            print(f"❌ Failed to parse menu data as JSON.")
            return None

    def _format_menu_items(self, menu_data: Dict[str, Any], thumbnail_data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Format menu data to the required structure

        Args:
            menu_data: Menu data dictionary from the API
            thumbnail_data: Thumbnail data dictionary from the API

        Returns:
            Dictionary with formatted menu items by section
        """
        formatted_menu = {}

        # Process each section (entrees, sides, drinks, nonFoodItems)
        for section in MENU_SECTIONS:
            if section in menu_data:
                # Create a list to hold the formatted items for this section
                formatted_menu[section] = []

                # Process each item in the section
                for item in menu_data[section]:
                    # Skip items that are not available
                    if item.get("isAvailable", True) is False:
                        continue

                    # Extract price from the item - Chipotle prices are already in dollars, not cents
                    price_amount = item.get("unitPrice", 0)

                    # For Chipotle, use the price directly as it's already in dollars
                    # Format the display price with $ sign
                    display_price = f"${price_amount:.2f}" if price_amount else "$0.00"

                    # Get item ID for thumbnail lookup
                    item_id = item.get("itemId", "")

                    # Get thumbnail URL if available
                    image_url = ""
                    if item_id and item_id in thumbnail_data and "thumbnailUrl" in thumbnail_data[item_id]:
                        image_url = thumbnail_data[item_id]["thumbnailUrl"]

                    # Get description if available
                    description = ""
                    if item_id and item_id in thumbnail_data and "description" in thumbnail_data[item_id]:
                        description = thumbnail_data[item_id]["description"]

                    formatted_item = {
                        "name": item.get("itemName", ""),
                        "price": price_amount,
                        "displayPrice": display_price,
                        "images": image_url,  # Just the URL string
                        "description": description,
                        "itemCategory": item.get("itemCategory", ""),
                        "itemType": item.get("itemType", "")
                    }

                    # Add primaryFillingName for entrees if available
                    if section == "entrees" and "primaryFillingName" in item:
                        formatted_item["primaryFillingName"] = item["primaryFillingName"]

                    formatted_menu[section].append(formatted_item)

        return formatted_menu

    def _create_combined_data(self, zipcode: str, store_info: Dict[str, Any], menu_data: Dict[str, Any], thumbnail_data: Dict[str, Any], sequence: int = 0) -> Dict[str, Any]:
        """Create a combined data structure with store and menu information

        Args:
            zipcode: Zip code used for the search
            store_info: Store information dictionary
            menu_data: Menu data dictionary
            thumbnail_data: Thumbnail data dictionary
            sequence: Original sequence of the store from the API

        Returns:
            Combined data dictionary
        """
        # Format menu items
        formatted_menu = self._format_menu_items(menu_data, thumbnail_data)

        # Flatten menu items into products list
        products = []
        for section, items in formatted_menu.items():
            products.extend(items)

        # Check for postal code in various possible field names
        postal_code = (
            store_info.get("postal_code", "") or
            store_info.get("postalCode", "") or
            store_info.get("zip_code", "") or
            store_info.get("zipCode", "") or
            store_info.get("zip", "")
        )
        # If postal_code is not available in any form, use the zipcode
        if not postal_code:
            postal_code = zipcode

        # Process the postal_code to simplify it if it's in the format "90123-1234"
        if postal_code and "-" in postal_code:
            # Split by hyphen and take only the first part
            postal_code = postal_code.split("-")[0]

        # Process zipcode if it's in the format "90123-1234"
        processed_zipcode = zipcode
        if processed_zipcode and "-" in processed_zipcode:
            # Split by hyphen and take only the first part
            processed_zipcode = processed_zipcode.split("-")[0]

        # Create the combined data structure
        return {
            "zipcode": processed_zipcode,
            # Store information
            "restaurant_number": str(store_info.get("restaurant_number", "")),  # Ensure it's a string
            "restaurant_name": store_info.get("restaurant_name", ""),
            "address1": store_info.get("address1", ""),
            "city": store_info.get("city", ""),
            "state": store_info.get("state", ""),
            "postal_code": postal_code,  # Include postal_code in the combined data
            "sequence": sequence,  # Original sequence from API
            "latitude": store_info.get("latitude", ""),
            "longitude": store_info.get("longitude", ""),
            # Menu information
            "products": products
        }

    def save_combined_data(self, zipcode: str, store_info: Dict[str, Any], menu_data: Dict[str, Any], thumbnail_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Save store and menu data to a single JSON file

        Args:
            zipcode: Zip code used for the search
            store_info: Store information dictionary
            menu_data: Menu data dictionary
            thumbnail_data: Thumbnail data dictionary
            output_path: Optional custom output path

        Returns:
            Path to the saved file or empty string if failed
        """
        if not store_info or not menu_data:
            print("❌ Missing data, cannot save combined file.")
            return ""

        # Create the combined data structure
        combined_data = self._create_combined_data(zipcode, store_info, menu_data, thumbnail_data)

        # Use provided output path or generate default filename
        if output_path:
            filename = output_path
            # Create directory if it doesn't exist and if there is a directory component
            output_dir = os.path.dirname(output_path)
            if output_dir:  # Only create directories if there's a directory component
                os.makedirs(output_dir, exist_ok=True)
        else:
            filename = COMBINED_FILE_TEMPLATE.format(
                zipcode=zipcode,
                restaurant_number=store_info.get("restaurant_number", "unknown")
            )

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(combined_data, f, indent=4)
            print(f"✅ Combined data saved to {os.path.abspath(filename)}")
            return filename
        except Exception as e:
            print(f"❌ Failed to save combined data: {str(e)}")
            return ""

    def display_stores(self, stores: List[Dict[str, Any]]) -> None:
        """Display stores in a tabular format

        Args:
            stores: List of store dictionaries
        """
        if not stores:
            print("No stores found.")
            return

        # Print table header
        header_format = "{:<3} {:<15} {:<30} {:<6} {:<15} {:<30} {:<10}"
        print("\n" + "=" * 110)
        print(header_format.format(*STORE_TABLE_HEADERS))
        print("-" * 110)

        # Print each store
        for i, store in enumerate(stores):
            distance_value = store.get("distance", "N/A")

            # Format the row data
            row_data = [
                i + 1,
                store.get("restaurant_number", "N/A"),
                store.get("restaurant_name", "N/A"),
                store.get("state", "N/A"),
                store.get("city", "N/A"),
                store.get("address1", "N/A"),
                f"{distance_value} mi" if distance_value != "N/A" else "N/A"
            ]

            print(header_format.format(*row_data))

        print("=" * 110 + "\n")

    def select_store(self, stores: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Allow user to select a store from the list

        Args:
            stores: List of store dictionaries

        Returns:
            Selected store dictionary or None if selection failed
        """
        if not stores:
            return None

        # Display stores
        self.display_stores(stores)

        # Get user selection
        while True:
            try:
                selection = input("Enter the number of the store you want to select (or 'q' to quit): ")

                if selection.lower() == 'q':
                    return None

                index = int(selection) - 1
                if 0 <= index < len(stores):
                    return stores[index]
                else:
                    print(f"Invalid selection. Please enter a number between 1 and {len(stores)}.")
            except ValueError:
                print("Invalid input. Please enter a number or 'q' to quit.")

    def crawl_chipotle(self, zipcode: str, output_path: Optional[str] = None, mode: str = MODE_DEFAULT) -> Dict[str, Any]:
        """Crawl Chipotle data for a given zip code

        Args:
            zipcode: Zip code to search for
            output_path: Optional output file path
            mode: Operation mode (default, test, or show)

        Returns:
            Dictionary with the combined data or error information
        """
        # Fetch stores for the zipcode
        stores = self.fetch_stores(zipcode)

        if not stores:
            return {"error": "No stores found for the provided zip code"}

        # Fetch menu metadata (thumbnails and descriptions)
        thumbnail_data = self.fetch_menu_metadata()

        # Handle different modes
        if mode == MODE_SHOW:
            # Show stores and let user select
            selected_store = self.select_store(stores)
            if not selected_store:
                return {"error": "Store selection cancelled"}

            restaurant_number = str(selected_store.get("restaurant_number", ""))  # Ensure it's a string
            menu_data = self.fetch_menu(restaurant_number)

            if not menu_data:
                return {"error": "Failed to fetch menu data"}

            # Create the combined data
            combined_data = self._create_combined_data(zipcode, selected_store, menu_data, thumbnail_data)

            # Always save the data, using default filename if no output path is provided
            saved_path = self.save_combined_data(zipcode, selected_store, menu_data, thumbnail_data, output_path)
            if saved_path:
                print(f"✅ Results saved to {os.path.abspath(saved_path)}")

            # Save to database
            db_result = save_chipotle_data_to_db(combined_data)
            if db_result.get("success"):
                print(f"✅ {db_result.get('message')}")
            else:
                print(f"⚠️ {db_result.get('message')}")

            return combined_data

        elif mode == MODE_TEST:
            # Get data for two stores (or as many as available, up to two)
            test_stores = stores[:min(2, len(stores))]
            combined_results = []

            for store in test_stores:
                restaurant_number = str(store.get("restaurant_number", ""))  # Ensure it's a string
                menu_data = self.fetch_menu(restaurant_number)

                if menu_data:
                    combined_results.append(self._create_combined_data(zipcode, store, menu_data, thumbnail_data))

            if not combined_results:
                return {"error": "Failed to fetch menu data for any store"}

            # Save the combined results if output path is provided
            if output_path:
                filename = output_path
            else:
                filename = TEST_COMBINED_FILE_TEMPLATE.format(zipcode=zipcode)

            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(combined_results, f, indent=4)
                print(f"✅ Test data for {len(combined_results)} stores saved to {os.path.abspath(filename)}")
            except Exception as e:
                print(f"❌ Failed to save test data: {str(e)}")

            return combined_results[0] if len(combined_results) == 1 else {"stores": combined_results}

        else:  # Default mode
            # Process all stores
            combined_results = []

            for idx, store in enumerate(stores):
                restaurant_number = str(store.get("restaurant_number", ""))

                # Fetch menu data for the store
                menu_data = self.fetch_menu(restaurant_number)

                if menu_data:
                    # Create the combined data with sequence information
                    combined_data = self._create_combined_data(zipcode, store, menu_data, thumbnail_data, sequence=idx)
                    combined_results.append(combined_data)

                    # Save to database
                    db_result = save_chipotle_data_to_db(combined_data)
                    if db_result.get("success"):
                        print(f"✅ {db_result.get('message')}")
                    else:
                        print(f"⚠️ {db_result.get('message')}")

            if not combined_results:
                return {"error": "Failed to fetch menu data for any store"}

            # Save the combined results if output path is provided
            if output_path:
                filename = output_path
                # Create directory if it doesn't exist
                output_dir = os.path.dirname(output_path)
                if output_dir:
                    os.makedirs(output_dir, exist_ok=True)

                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(combined_results, f, indent=4)
                    print(f"✅ Data for {len(combined_results)} stores saved to {os.path.abspath(filename)}")
                except Exception as e:
                    print(f"❌ Failed to save data: {str(e)}")

            # Return all results
            return {"stores": combined_results}


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Chipotle Store and Menu Fetcher")
    parser.add_argument("zipcode", help="Zipcode to search for Chipotle stores")
    parser.add_argument("--output", help="Output file path (optional)")
    parser.add_argument("--mode", choices=[MODE_DEFAULT, MODE_TEST, MODE_SHOW], default=MODE_DEFAULT,
                        help=f"Operation mode: {MODE_DEFAULT} (automatic), {MODE_TEST} (fetch two stores), or {MODE_SHOW} (select store)")
    return parser.parse_args()


def main():
    # Parse command line arguments
    args = parse_arguments()
    zipcode = args.zipcode
    output = args.output
    mode = args.mode

    # Create API client and run the crawler
    api = ChipotleAPI()
    result = api.crawl_chipotle(zipcode, output, mode)

    # Check for errors
    if isinstance(result, dict) and "error" in result:
        print(f"❌ {result['error']}")
        return

    print("✅ Successfully fetched Chipotle data")


if __name__ == "__main__":
    main()