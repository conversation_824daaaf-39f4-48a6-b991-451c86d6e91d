"""
Database Statistics Script

This script analyzes the database and provides statistics on:
1. Number of stores per restaurant
2. Number of stores per searched_code
3. Number of unique searched_codes
4. Number of items per store (only latest crawled data)
5. Percentage of null values for each column in the items table (only latest crawled data)
"""

import os
import sys
import pandas as pd
from sqlalchemy import create_engine, text, func, inspect, distinct, and_, desc
from sqlalchemy.orm import sessionmaker, aliased
from dotenv import load_dotenv
from tabulate import tabulate
from colorama import init, Fore, Style
from datetime import datetime

# Add the parent directory to sys.path to import from db
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.database import engine, SessionLocal
from db.models import Store, Item

# Initialize colorama for colored console output
init()

def get_latest_crawled_items(session):
    """
    Get the latest crawled items for each store

    This function returns a subquery that can be used to filter items
    to only include the latest crawled data for each store.
    """
    # Get the latest crawl date for each store
    latest_crawl_dates = session.query(
        Item.store_id,
        func.max(Item.crawled_at).label('latest_crawl_date')
    ).group_by(Item.store_id).subquery()

    # Get items with the latest crawl date for each store
    latest_items = session.query(Item).join(
        latest_crawl_dates,
        and_(
            Item.store_id == latest_crawl_dates.c.store_id,
            Item.crawled_at == latest_crawl_dates.c.latest_crawl_date
        )
    )

    return latest_items

def print_header(title):
    """Print a formatted header"""
    print(f"\n{Fore.CYAN}{'=' * 80}")
    print(f"{title.center(80)}")
    print(f"{'=' * 80}{Style.RESET_ALL}\n")

def get_stores_per_restaurant(session):
    """Count the number of stores per restaurant"""
    print_header("STORES PER RESTAURANT")

    # Query to count stores per restaurant
    result = session.query(
        Store.restaurant,
        func.count(Store.id).label('store_count')
    ).group_by(Store.restaurant).all()

    # Convert to DataFrame for better display
    df = pd.DataFrame(result, columns=['Restaurant', 'Store Count'])

    # Add total row
    total = df['Store Count'].sum()
    df.loc[len(df)] = ['TOTAL', total]

    # Print table
    print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))

    return df

def get_stores_per_searched_code(session):
    """Count the number of stores per searched_code"""
    print_header("STORES PER SEARCHED CODE")

    # Query to count stores per searched_code
    result = session.query(
        Store.searched_code,
        func.count(Store.id).label('store_count')
    ).group_by(Store.searched_code).order_by(func.count(Store.id).desc()).all()

    # Convert to DataFrame for better display
    df = pd.DataFrame(result, columns=['Searched Code', 'Store Count'])

    # Add total row
    total = df['Store Count'].sum()
    df.loc[len(df)] = ['TOTAL', total]

    # Print table
    print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))

    return df

def get_unique_searched_codes(session):
    """Count the number of unique searched_codes"""
    print_header("UNIQUE SEARCHED CODES")

    # Query to count unique searched_codes
    unique_count = session.query(func.count(func.distinct(Store.searched_code))).scalar()

    print(f"{Fore.GREEN}Number of unique searched codes: {unique_count}{Style.RESET_ALL}")

    return unique_count

def get_items_per_store(session):
    """Count the number of items per store (only latest crawled data)"""
    print_header("ITEMS PER STORE (LATEST CRAWLED DATA ONLY)")

    # Get the latest crawled items subquery
    latest_items = get_latest_crawled_items(session).subquery()

    # Query to count items per store using only the latest crawled data
    result = session.query(
        Store.id,
        Store.restaurant,
        Store.restaurant_number,
        Store.store_code,
        func.count(latest_items.c.id).label('item_count'),
        func.max(latest_items.c.crawled_at).label('crawl_date')
    ).outerjoin(latest_items, Store.id == latest_items.c.store_id).group_by(
        Store.id, Store.restaurant, Store.restaurant_number, Store.store_code
    ).order_by(func.count(latest_items.c.id).desc()).all()

    # Convert to DataFrame for better display
    df = pd.DataFrame(result, columns=['Store ID', 'Restaurant', 'Restaurant Number', 'Store Code', 'Item Count', 'Crawl Date'])

    # Format the crawl date
    df['Crawl Date'] = df['Crawl Date'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if x else 'N/A')

    # Calculate statistics
    stats = {
        'Average': df['Item Count'].mean(),
        'Median': df['Item Count'].median(),
        'Min': df['Item Count'].min(),
        'Max': df['Item Count'].max(),
        'Total Items': df['Item Count'].sum()
    }

    # Print table (all rows)
    print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))

    # Print statistics
    print(f"\n{Fore.GREEN}Item Count Statistics (Latest Crawled Data Only):{Style.RESET_ALL}")
    for stat, value in stats.items():
        print(f"{stat}: {value:.2f}" if isinstance(value, float) else f"{stat}: {value}")

    return df

def get_null_value_percentages(session):
    """Calculate the percentage of null values for each column in the items table (only latest crawled data)"""
    print_header("NULL VALUE PERCENTAGES IN ITEMS TABLE (LATEST CRAWLED DATA ONLY)")

    # Get the latest crawled items
    latest_items = get_latest_crawled_items(session)

    # Get total number of latest items
    total_items = latest_items.count()

    if total_items == 0:
        print(f"{Fore.RED}No items found in the database.{Style.RESET_ALL}")
        return None

    # Get columns from the Item model
    columns = [column.name for column in inspect(Item).columns]

    # Calculate null percentages for each column
    null_percentages = {}
    for column in columns:
        # Skip id, store_id, created_at, updated_at, crawled_at
        if column in ['id', 'store_id', 'created_at', 'updated_at', 'crawled_at']:
            continue

        # Count null values for the column in the latest items
        null_count = latest_items.filter(text(f"{column} IS NULL")).count()

        # Calculate percentage
        percentage = (null_count / total_items) * 100
        null_percentages[column] = percentage

    # Convert to DataFrame for better display
    df = pd.DataFrame(list(null_percentages.items()), columns=['Column', 'Null Percentage'])
    df = df.sort_values('Null Percentage', ascending=False)

    # Format percentage
    df['Null Percentage'] = df['Null Percentage'].apply(lambda x: f"{x:.2f}%")

    # Print table
    print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))

    return df

def get_crawl_date_stats(session):
    """Get statistics about crawl dates"""
    print_header("CRAWL DATE STATISTICS")

    # Get total number of unique crawl dates
    unique_crawl_dates = session.query(func.count(func.distinct(Item.crawled_at))).scalar()

    # Get the earliest and latest crawl dates
    earliest_crawl = session.query(func.min(Item.crawled_at)).scalar()
    latest_crawl = session.query(func.max(Item.crawled_at)).scalar()

    # Get the number of stores with multiple crawl dates
    stores_with_multiple_crawls = session.query(
        func.count(func.distinct(Item.store_id))
    ).filter(
        Item.store_id.in_(
            session.query(Item.store_id)
            .group_by(Item.store_id)
            .having(func.count(func.distinct(Item.crawled_at)) > 1)
        )
    ).scalar()

    # Print the statistics
    print(f"{Fore.GREEN}Total unique crawl dates: {unique_crawl_dates}{Style.RESET_ALL}")
    if earliest_crawl:
        print(f"{Fore.GREEN}Earliest crawl date: {earliest_crawl.strftime('%Y-%m-%d %H:%M:%S')}{Style.RESET_ALL}")
    if latest_crawl:
        print(f"{Fore.GREEN}Latest crawl date: {latest_crawl.strftime('%Y-%m-%d %H:%M:%S')}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Stores with multiple crawl dates: {stores_with_multiple_crawls}{Style.RESET_ALL}")

    # Get the distribution of crawl dates per store
    # First, get the count of crawl dates per store
    store_crawl_counts = session.query(
        Item.store_id,
        func.count(func.distinct(Item.crawled_at)).label('crawl_date_count')
    ).group_by(Item.store_id).subquery()

    # Then, count how many stores have each number of crawl dates
    crawl_date_counts = session.query(
        store_crawl_counts.c.crawl_date_count,
        func.count().label('store_count')
    ).group_by(
        store_crawl_counts.c.crawl_date_count
    ).order_by(
        store_crawl_counts.c.crawl_date_count
    ).all()

    if crawl_date_counts:
        print(f"\n{Fore.GREEN}Distribution of crawl dates per store:{Style.RESET_ALL}")
        df = pd.DataFrame(crawl_date_counts, columns=['Crawl Dates', 'Store Count'])
        print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))

    return {
        'unique_crawl_dates': unique_crawl_dates,
        'earliest_crawl': earliest_crawl,
        'latest_crawl': latest_crawl,
        'stores_with_multiple_crawls': stores_with_multiple_crawls
    }

def main():
    """Main function to run all statistics"""
    try:
        # Create a database session
        session = SessionLocal()

        print(f"{Fore.GREEN}Starting database statistics analysis...{Style.RESET_ALL}")

        # Check if there are any stores in the database
        store_count = session.query(func.count(Store.id)).scalar()
        print(f"{Fore.YELLOW}Total stores in database: {store_count}{Style.RESET_ALL}")

        # Check if there are any items in the database
        item_count = session.query(func.count(Item.id)).scalar()
        print(f"{Fore.YELLOW}Total items in database: {item_count}{Style.RESET_ALL}")

        if store_count == 0:
            print(f"{Fore.RED}No stores found in the database. Please make sure your database is populated.{Style.RESET_ALL}")
            return

        # Get crawl date statistics
        if item_count > 0:
            get_crawl_date_stats(session)

        # Run all statistics functions
        get_stores_per_restaurant(session)
        get_stores_per_searched_code(session)
        get_unique_searched_codes(session)
        get_items_per_store(session)

        if item_count > 0:
            get_null_value_percentages(session)
        else:
            print(f"{Fore.RED}No items found in the database. Skipping null value percentage analysis.{Style.RESET_ALL}")

        print(f"\n{Fore.GREEN}Database statistics analysis completed!{Style.RESET_ALL}")

    except Exception as e:
        print(f"{Fore.RED}Error: {str(e)}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()
    finally:
        session.close()

if __name__ == "__main__":
    print("Script started")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Database URL: {os.getenv('DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432/menu_db')}")

    try:
        # Test database connection
        print("Testing database connection...")
        connection = engine.connect()
        print("Database connection successful!")
        connection.close()
    except Exception as e:
        print(f"Database connection error: {str(e)}")
        import traceback
        traceback.print_exc()

    main()
