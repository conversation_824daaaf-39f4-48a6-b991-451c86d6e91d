from flask import Flask, request, jsonify
from pizzahut_crawler import PizzaHutCrawler
from chipotle import chipotleCrawler
import os
import json

app = Flask(__name__)

@app.route("/crawl/pizzahut", methods=["POST"])
def crawl_pizzahut():
    zip_code = request.json.get("zip_code")
    output = request.json.get("output", f"output/pizzahut_{zip_code}.json")
    headless = request.json.get("headless", True)

    crawler = PizzaHutCrawler(headless=headless)
    try:
        result = crawler.crawl_menu(zip_code, output)
        return jsonify({"status": "success", "data": result})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        crawler.close()

@app.route("/crawl/chipotle", methods=["POST"])
def crawl_chipotle():
    zip_code = request.json.get("zip_code")
    output = request.json.get("output", f"output/chipotle_{zip_code}.json")
    headless = request.json.get("headless", True)

    crawler = chipotleCrawler(headless=headless)
    try:
        result = crawler.crawl_chipotle(zip_code, output)
        return jsonify({"status": "success", "data": result})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/results/pizzahut/<zip_code>", methods=["GET"])
def get_pizzahut_results(zip_code):
    path = f"output/pizzahut_{zip_code}.json"
    if os.path.exists(path):
        with open(path, "r") as f:
            return jsonify(json.load(f))
    return jsonify({"error": "Result not found"}), 404

@app.route("/results/chipotle/<zip_code>", methods=["GET"])
def get_chipotle_results(zip_code):
    path = f"output/chipotle_{zip_code}.json"
    if os.path.exists(path):
        with open(path, "r") as f:
            return jsonify(json.load(f))
    return jsonify({"error": "Result not found"}), 404

if __name__ == "__main__":
    os.makedirs("output", exist_ok=True)
    app.run(debug=True, port=5000)
