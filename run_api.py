import os
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

if __name__ == "__main__":
    # Get port from environment variable or use default
    port = int(os.getenv("PORT", 8000))

    print(f"🚀 Starting Restaurant Menu API on port {port}...")
    print(f"📚 API documentation will be available at http://localhost:{port}/docs")

    # Run the application
    uvicorn.run("api.main:app", host="0.0.0.0", port=port, reload=True)
