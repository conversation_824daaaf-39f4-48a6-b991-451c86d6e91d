from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any, List
import subprocess
import sys
import os
from db.database import get_db
from api.services.menu_service import MenuService
from db.schemas import MenuResponse

router = APIRouter(
    prefix="/menu",
    tags=["menu"],
    responses={404: {"description": "Not found"}},
)

@router.get("/", response_model=MenuResponse, summary="Get menu for a specific store")
async def get_menu(
    store_id: Optional[int] = Query(None, description="Database ID of the store to get menu for"),
    restaurant_number: Optional[str] = Query(None, description="Restaurant's number (e.g., '026196' for Pizza Hut)"),
    restaurant: Optional[str] = Query(None, description="Restaurant name (e.g., 'pizzahut', 'chipotle')"),
    zipcode: Optional[str] = Query(None, description="Zip code to search for stores"),
    db: Session = Depends(get_db)
):
    """
    Get menu for a specific store

    You can provide:
    - store_id to get menu by database ID
    - restaurant_number to get menu by the restaurant's number
    - restaurant and zipcode to get menu by restaurant name and zip code
    - restaurant to get menu for the first store of that restaurant
    - zipcode to get menu for any store in that zip code (searches store_code first, then searched_code, sorted by sequence)
    """
    # Process zipcode if it's in the format "90123-1234"
    if zipcode and "-" in zipcode:
        # Split by hyphen and take only the first part
        zipcode = zipcode.split("-")[0]
    # Case 1: Get by database ID
    if store_id is not None:
        menu = MenuService.get_menu_by_id(db, store_id)
        if not menu:
            raise HTTPException(status_code=404, detail=f"Store with database ID {store_id} not found")
        return menu

    # Case 2: Get by restaurant's number
    if restaurant_number is not None:
        menu = MenuService.get_menu_by_store_id(db, restaurant_number)
        if not menu:
            raise HTTPException(status_code=404, detail=f"Store with restaurant number {restaurant_number} not found")
        return menu

    # Case 2: Get by restaurant and zipcode
    if restaurant is not None and zipcode is not None:
        menu = MenuService.get_menu_by_restaurant_and_zipcode(db, restaurant, zipcode)
        if not menu:
            raise HTTPException(status_code=404, detail=f"No menu found for {restaurant} in zip code {zipcode}")
        return menu

    # Case 3: Get by restaurant only
    if restaurant is not None:
        menu = MenuService.get_menu_by_restaurant(db, restaurant)
        if not menu:
            raise HTTPException(status_code=404, detail=f"No menu found for restaurant: {restaurant}")
        return menu

    # Case 4: Get by zipcode only
    if zipcode is not None:
        # Try to find any store in this zipcode
        menus = MenuService.get_menu_by_zipcode(db, zipcode)
        if menus and len(menus) > 0:
            # Return the first menu found
            return menus[0]
        raise HTTPException(status_code=404, detail=f"No menu found for zip code: {zipcode}")

    # If no parameters provided
    raise HTTPException(status_code=400, detail="You must provide either store_id, restaurant_number, restaurant, zipcode, or restaurant and zipcode")
