from sqlalchemy.orm import Session
from typing import Optional, List
from db.models import Store
from db.repositories.store_repository import StoreRepository
from db.repositories.item_repository import ItemRepository
from db.schemas import MenuResponse

class MenuService:
    @staticmethod
    def get_menu_by_id(db: Session, store_id: int) -> Optional[MenuResponse]:
        """
        Get menu for a specific store by database ID

        Args:
            db: Database session
            store_id: Database ID of the store

        Returns:
            MenuResponse object or None if store not found
        """
        # Get store from database
        store = StoreRepository.get_by_id(db, store_id)
        if not store:
            return None

        # Get items for the store
        items = ItemRepository.get_by_store_id(db, store_id)

        # Set the items on the store
        store.items = items

        # Return menu response
        return MenuResponse(
            store=store
        )

    @staticmethod
    def get_menu_by_store_id(db: Session, restaurant_number: str) -> Optional[MenuResponse]:
        """
        Get menu for a specific store by restaurant's number

        Args:
            db: Database session
            restaurant_number: Restaurant's number (e.g., '026196' for Pizza Hut)

        Returns:
            MenuResponse object or None if store not found
        """
        # Get store from database
        store = StoreRepository.get_by_restaurant_number(db, restaurant_number)
        if not store:
            return None

        # Get items for the store
        items = ItemRepository.get_by_store_id(db, store.id)

        # Set the items on the store
        store.items = items

        # Return menu response
        return MenuResponse(
            store=store
        )

    @staticmethod
    def get_menu_by_restaurant(db: Session, restaurant: str) -> Optional[MenuResponse]:
        """
        Get menu for a restaurant (returns the first store found for that restaurant)

        Args:
            db: Database session
            restaurant: Restaurant name (e.g., "pizzahut", "chipotle")

        Returns:
            MenuResponse object or None if no stores found for the restaurant
        """
        # Find stores for the restaurant
        stores = StoreRepository.get_by_restaurant(db, restaurant.lower())
        if not stores or len(stores) == 0:
            return None

        # Get the first store
        store = stores[0]

        # Get items for the store
        items = ItemRepository.get_by_store_id(db, store.id)

        # Set the items on the store
        store.items = items

        # Return menu response
        return MenuResponse(
            store=store
        )

    @staticmethod
    def get_menu_by_restaurant_and_zipcode(db: Session, restaurant: str, zipcode: str) -> Optional[MenuResponse]:
        """
        Get menu for a restaurant in a specific zip code

        Args:
            db: Database session
            restaurant: Restaurant name (e.g., "pizzahut", "chipotle")
            zipcode: Zip code to search for

        Returns:
            MenuResponse object or None if no stores found for the restaurant in the zip code
        """
        # Find stores for the restaurant in the zip code
        # The repository method handles searching by store_code first, then searched_code, and sorting by sequence
        stores = StoreRepository.get_by_restaurant_and_zipcode(db, restaurant.lower(), zipcode)
        if not stores or len(stores) == 0:
            return None

        # Get the first store (lowest sequence)
        store = stores[0]

        # Get items for the store
        items = ItemRepository.get_by_store_id(db, store.id)

        # Set the items on the store
        store.items = items

        # Return menu response
        return MenuResponse(
            store=store
        )

    @staticmethod
    def get_menu_by_zipcode(db: Session, zipcode: str) -> List[MenuResponse]:
        """
        Get menus for all restaurants in a specific zip code

        Args:
            db: Database session
            zipcode: Zip code to search for

        Returns:
            List of MenuResponse objects for all stores in the zip code
        """
        # Find all stores in the zip code (first by store_code, then by searched_code)
        # The repository method handles the search order and sorting by sequence
        stores = StoreRepository.get_by_zipcode(db, zipcode)

        if not stores or len(stores) == 0:
            return []

        # Create menu responses for each store, sorted by sequence
        menu_responses = []
        for store in stores:
            # Get items for the store
            items = ItemRepository.get_by_store_id(db, store.id)

            # Set the items on the store
            store.items = items

            # Create menu response
            menu_response = MenuResponse(
                store=store
            )
            menu_responses.append(menu_response)

        return menu_responses
