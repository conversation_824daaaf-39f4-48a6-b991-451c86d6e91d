# API URLs
RESTAURANT_API_URL = "https://services.chipotle.com/restaurant/v3/restaurant"
MENU_API_URL_TEMPLATE = "https://services.chipotle.com/menuinnovation/v1/restaurants/{restaurant_number}/onlinemenu"
METADATA_API_URL = "https://services.chipotle.com/menu-metadata/v1/menu-metadata"

# Request headers
RESTAURANT_HEADERS = {
    "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
    "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
}

MENU_HEADERS = {
    "Accept": "application/json, text/plain, */*",
    "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
    "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
}

METADATA_HEADERS = {
    "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
    "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
}

# File name templates
STORE_FILE_TEMPLATES = [
    "chipotle_stores_{zipcode}.json",
    "chipotle_stores_zip_{zipcode}.json"
]

COMBINED_FILE_TEMPLATE = "chipotle_combined_{zipcode}_{restaurant_number}.json"

# Table headers for display
STORE_TABLE_HEADERS = ["#", "Restaurant Number", "Restaurant Name", "State", "City", "Street", "Distance"]

# Mode constants
MODE_TEST = "test"
MODE_SHOW = "show"
MODE_DEFAULT = "default"

# Test mode combined file template
TEST_COMBINED_FILE_TEMPLATE = "chipotle_test_{zipcode}.json"

# Menu sections to include
MENU_SECTIONS = ["entrees", "sides", "drinks", "nonFoodItems"]
