import json
import re
import threading
import time
from collections import defaultdict
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def extract_zip(full_address):
    match = re.search(r"\b(\d{5})\b", full_address)
    return match.group(1) if match else "unknown"

def scrape_menu_for_branch(index, card_html, zip_code, results, lock):
    options = webdriver.ChromeOptions()
    options.add_argument("--headless=new")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    wait = WebDriverWait(driver, 20)

    try:
        print(f"[INFO] Scraping branch {index + 1}")
        driver.get("https://www.subway.com/en-us/locator")

        input_box = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input.searchLocationInput"))
        )
        input_box.clear()
        input_box.send_keys(zip_code)
        input_box.send_keys(Keys.RETURN)
        time.sleep(5)

        branch_cards = driver.find_elements(By.CSS_SELECTOR, ".location")
        card = branch_cards[index]

        order_button = card.find_element(By.CSS_SELECTOR, "button.orderOnlineAction")
        driver.execute_script("arguments[0].click();", order_button)
        time.sleep(5)

        full_menu = []
        category_elements = driver.find_elements(By.CSS_SELECTOR, "section.component_menu")
        for section in category_elements:
            try:
                label_elem = section.find_element(By.CSS_SELECTOR, ".component_menu_label")
                subtitle_elem = section.find_elements(By.CSS_SELECTOR, ".menu-subtitle")
                label = label_elem.text.strip()
                subtitle = subtitle_elem[0].text.strip() if subtitle_elem else ""
                full_menu.append({"category": label, "description": subtitle})
            except Exception as e:
                print(f"[WARN] Skipped a category: {e}")

        branch_address_elem = card.find_element(By.CSS_SELECTOR, ".cityStateAddress")
        branch_address = branch_address_elem.text.strip()

        with lock:
            results[zip_code][branch_address] = full_menu

    except Exception as e:
        print(f"[ERROR] Failed branch {index + 1}: {e}")
    finally:
        driver.quit()

def crawl_subway_all_parallel(zip_code):
    options = webdriver.ChromeOptions()
    options.add_argument("--headless=new")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    wait = WebDriverWait(driver, 20)

    zip_menus = defaultdict(dict)
    lock = threading.Lock()

    try:
        driver.get("https://www.subway.com/en-us")
        print("[INFO] Opened Subway homepage")

        try:
            menu_button = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'Menu')]"))
            )
            driver.execute_script("arguments[0].click();", menu_button)
            print("[INFO] Clicked Menu button")
        except Exception:
            print("[WARN] Menu button not found or not clickable")
            return

        try:
            WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button.start_order"))
            ).click()
            print("[INFO] Clicked Start Order")
        except Exception:
            print("[WARN] Start Order button not found or not clickable")
            return

        input_box = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input.searchLocationInput"))
        )
        input_box.clear()
        input_box.send_keys(zip_code)
        input_box.send_keys(Keys.RETURN)
        print(f"[INFO] Entered zip code: {zip_code}")
        time.sleep(5)

        branch_cards = driver.find_elements(By.CSS_SELECTOR, ".location")
        print(f"[INFO] Found {len(branch_cards)} branches")

        threads = []
        for i in range(len(branch_cards)):
            t = threading.Thread(target=scrape_menu_for_branch, args=(i, branch_cards[i].get_attribute('outerHTML'), zip_code, zip_menus, lock))
            t.start()
            threads.append(t)

        for t in threads:
            t.join()

        with open("subway_menu_by_zip.json", "w") as f:
            json.dump(zip_menus, f, indent=2)

        print("[INFO] Scraping completed and saved to subway_menu_by_zip.json")

    finally:
        driver.quit()

if __name__ == "__main__":
    crawl_subway_all_parallel("92010")
