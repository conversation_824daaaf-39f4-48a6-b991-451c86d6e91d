import requests
import json
import os
import argparse
import sys
from typing import Dict, Any, List, Optional, Tuple, Union
from .const import (
    STORES_API_URL,
    MENU_API_URL_TEMPLATE,
    STORES_HEADERS,
    MENU_HEADERS,
    OCCASION_ID,
    STORE_FILE_TEMPLATES,
    COMBINED_FILE_TEMPLATE,
    STORE_TABLE_HEADERS,
    MODE_TEST,
    MODE_SHOW,
    MODE_DEFAULT,
    TEST_COMBINED_FILE_TEMPLATE
)

# Add parent directory to path to import db_utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import save_pizzahut_data_to_db


class PizzaHutAPI:
    """Pizza Hut API client for fetching store and menu data"""

    def __init__(self, headless: bool = True):
        """
        Initialize the Pizza Hut API client

        Args:
            headless: Not used in API implementation, but kept for compatibility
        """
        # This parameter is not used in the API implementation
        # but kept for compatibility with the crawler interface
        self.headless = headless

    def fetch_stores(self, zipcode: str) -> List[Dict[str, Any]]:
        """
        Fetch Pizza Hut stores for the given zipcode

        Args:
            zipcode: Zip code to search for stores

        Returns:
            List of store data dictionaries
        """
        # Try to load existing store data first
        existing_files = [template.format(zipcode=zipcode) for template in STORE_FILE_TEMPLATES]

        for file_path in existing_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"📋 Loaded existing store data from {file_path}")
                    return data
                except:
                    print(f"⚠️ Could not load existing store data from {file_path}")

        print(f"⚠️ No existing store data found, fetching fresh data...")

        # Create payload with zipcode directly
        payload = {
            "occasion_id": OCCASION_ID,
            "postal_code": zipcode,
        }

        try:
            print(f"🔍 Searching for Pizza Hut stores with zipcode: {zipcode}...")
            response = requests.post(
                url=STORES_API_URL,
                headers=STORES_HEADERS,
                json=payload,
                cookies=requests.cookies.RequestsCookieJar()
            )

            response.raise_for_status()
            data = response.json()

            # Sort stores by distance
            if isinstance(data, list):
                data = sorted(data, key=lambda x: float(x.get("distance", {}).get("value", "999999")))
                print(f"📦 Found {len(data)} stores, sorted by distance")
                return data
            else:
                print("❌ Unexpected response format")
                return []

        except requests.exceptions.RequestException as err:
            print(f"❌ Request failed: {err}")
            return []
        except json.JSONDecodeError as err:
            print(f"❌ JSON parsing failed: {err}")
            return []

    def fetch_menu(self, store_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch menu data for the selected store

        Args:
            store_id: Store ID to fetch menu for

        Returns:
            Menu data dictionary or None if failed
        """
        url = MENU_API_URL_TEMPLATE.format(store_id=store_id)

        try:
            print(f"🔍 Fetching menu for store ID: {store_id}...")
            response = requests.get(url, headers=MENU_HEADERS)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as err:
            print(f"❌ Request failed: {err}")
            return None
        except json.JSONDecodeError:
            print(f"❌ Failed to parse menu data as JSON.")
            return None

    def _format_bundles(self, bundles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format bundles data to the required structure

        Args:
            bundles: List of bundle dictionaries from the API

        Returns:
            List of formatted bundle dictionaries
        """
        formatted_bundles = []
        for bundle in bundles:
            # Check if the bundle is visible and redeemable online
            visible_online = bundle.get("visibleOnline", True)
            redeemable_online = bundle.get("redeemableOnline", True)

            # Skip bundles that are not visible or not redeemable online
            if not visible_online or not redeemable_online:
                continue

            # Get the price amount from the price object
            price_obj = bundle.get("price", {"amount": 0, "currencyCode": "USD"})
            price_amount = price_obj.get("amount", 0) if isinstance(price_obj, dict) else 0

            # Extract image URL from the first image if available
            image_url = ""
            images = bundle.get("images", [])
            if images and isinstance(images, list) and len(images) > 0:
                first_image = images[0]
                if isinstance(first_image, dict) and "url" in first_image:
                    image_url = first_image["url"]

            formatted_bundle = {
                "name": bundle.get("name", ""),
                "price": price_amount,
                "images": image_url,  # Just the URL string instead of the full image object
                "description": bundle.get("description", ""),
                "displayPrice": bundle.get("displayPrice", ""),
            }
            formatted_bundles.append(formatted_bundle)

        return formatted_bundles

    def _format_products(self, products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format products data to the required structure

        Args:
            products: List of product dictionaries from the API

        Returns:
            List of formatted product dictionaries
        """
        formatted_products = []
        for product in products:
            # Skip NSLP products (National School Lunch Program)
            product_name = product.get("name", "")
            if "NSLP" in product_name:
                continue

            # Skip products with specific product code prefixes
            product_code = product.get("productCode", "")
            if product_code and product_code.startswith("CLSS140SC"):
                continue

            # Get the variants
            variants = product.get("variants", [])

            # Try to find the large hand-tossed variant (SIZE060L_BASE020H) as it's likely the standard option
            selected_variant = None
            for variant in variants:
                variant_code = variant.get("variantCode", "")
                if "SIZE060L_BASE020H" in variant_code:
                    selected_variant = variant
                    break

            # If we couldn't find the large hand-tossed variant, try to find any large variant
            if not selected_variant:
                for variant in variants:
                    variant_code = variant.get("variantCode", "")
                    if "SIZE060L" in variant_code:
                        selected_variant = variant
                        break

            # If we still couldn't find a suitable variant, use the first one
            if not selected_variant and variants:
                selected_variant = variants[0]
            elif not selected_variant:
                selected_variant = {}

            # Extract price from the selected variant or use default
            price_obj = selected_variant.get("price", {"amount": 0, "currencyCode": "USD"})
            price_amount = price_obj.get("amount", 0) if isinstance(price_obj, dict) else 0

            # Format the display price with $ sign and cents
            display_price = f"${price_amount/100:.2f}" if price_amount else "$0.00"

            # Extract image URL from the first image if available
            image_url = ""
            images = product.get("images", [])
            if images and isinstance(images, list) and len(images) > 0:
                first_image = images[0]
                if isinstance(first_image, dict) and "url" in first_image:
                    image_url = first_image["url"]

            formatted_product = {
                "name": product.get("name", ""),
                "price": price_amount,
                "displayPrice": display_price,
                "images": image_url,  # Just the URL string instead of the full image object
                "description": product.get("description", ""),
            }
            formatted_products.append(formatted_product)

        return formatted_products

    def _create_combined_data(self, zipcode: str, store_info: Dict[str, Any], menu_data: Dict[str, Any], sequence: int = 0) -> Dict[str, Any]:
        """Create a combined data structure with store and menu information

        Args:
            zipcode: Zip code used for the search
            store_info: Store information dictionary
            menu_data: Menu data dictionary
            sequence: Original sequence of the store from the API

        Returns:
            Combined data dictionary
        """
        # Format bundles and products
        formatted_bundles = self._format_bundles(menu_data.get("bundles", []))
        formatted_products = self._format_products(menu_data.get("products", []))

        # Check for postal code in various possible field names
        postal_code = (
            store_info.get("postal_code", "") or
            store_info.get("postalCode", "") or
            store_info.get("zip_code", "") or
            store_info.get("zipCode", "") or
            store_info.get("zip", "")
        )
        # If postal_code is not available in any form, use the zipcode
        if not postal_code:
            postal_code = zipcode

        # Process the postal_code to simplify it if it's in the format "90123-1234"
        if postal_code and "-" in postal_code:
            # Split by hyphen and take only the first part
            postal_code = postal_code.split("-")[0]

        # Process zipcode if it's in the format "90123-1234"
        processed_zipcode = zipcode
        if processed_zipcode and "-" in processed_zipcode:
            # Split by hyphen and take only the first part
            processed_zipcode = processed_zipcode.split("-")[0]

        # Create the combined data structure
        return {
            "zipcode": processed_zipcode,
            # Store information
            "address1": store_info.get("address1", ""),
            "city": store_info.get("city", ""),
            "state": store_info.get("state", ""),
            "store_id": store_info.get("store_id", ""),
            "store_number": store_info.get("store_number", ""),
            "postal_code": postal_code,  # Include postal_code in the combined data
            "sequence": sequence,  # Original sequence from API
            "latitude": store_info.get("latitude", ""),
            "longitude": store_info.get("longitude", ""),
            # Menu information
            "name": menu_data.get("name", ""),
            "bundles": formatted_bundles,
            "products": formatted_products
        }

    def save_combined_data(self, zipcode: str, store_info: Dict[str, Any], menu_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Save store and menu data to a single JSON file

        Args:
            zipcode: Zip code used for the search
            store_info: Store information dictionary
            menu_data: Menu data dictionary
            output_path: Optional custom output path

        Returns:
            Path to the saved file or empty string if failed
        """
        if not store_info or not menu_data:
            print("❌ Missing data, cannot save combined file.")
            return ""

        # Create the combined data structure
        combined_data = self._create_combined_data(zipcode, store_info, menu_data)

        # Use provided output path or generate default filename
        if output_path:
            filename = output_path
            # Create directory if it doesn't exist and if there is a directory component
            output_dir = os.path.dirname(output_path)
            if output_dir:  # Only create directories if there's a directory component
                os.makedirs(output_dir, exist_ok=True)
        else:
            filename = COMBINED_FILE_TEMPLATE.format(
                zipcode=zipcode,
                store_id=store_info.get("store_id", "unknown")
            )

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(combined_data, f, indent=4)
            print(f"✅ Combined data saved to {os.path.abspath(filename)}")
            return filename
        except Exception as e:
            print(f"❌ Failed to save combined data: {str(e)}")
            return ""

    def display_stores(self, stores: List[Dict[str, Any]]) -> None:
        """Display stores in a tabular format

        Args:
            stores: List of store dictionaries
        """
        if not stores:
            print("No stores found.")
            return

        # Print table header
        header_format = "{:<3} {:<10} {:<12} {:<6} {:<15} {:<30} {:<10}"
        print("\n" + "=" * 90)
        print(header_format.format(*STORE_TABLE_HEADERS))
        print("-" * 90)

        # Print each store
        for i, store in enumerate(stores):
            distance_obj = store.get("distance", {})
            distance_value = distance_obj.get("value", "N/A") if isinstance(distance_obj, dict) else "N/A"

            # Format the row data
            row_data = [
                i + 1,
                store.get("store_id", "N/A"),
                store.get("store_number", "N/A"),
                store.get("state", "N/A"),
                store.get("city", "N/A"),
                store.get("address1", "N/A"),
                f"{distance_value} mi" if distance_value != "N/A" else "N/A"
            ]

            print(header_format.format(*row_data))

        print("=" * 90 + "\n")

    def select_store(self, stores: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Allow user to select a store from the list

        Args:
            stores: List of store dictionaries

        Returns:
            Selected store dictionary or None if selection failed
        """
        if not stores:
            return None

        # Display stores
        self.display_stores(stores)

        # Get user selection
        while True:
            try:
                selection = input("Enter the number of the store you want to select (or 'q' to quit): ")

                if selection.lower() == 'q':
                    return None

                index = int(selection) - 1
                if 0 <= index < len(stores):
                    return stores[index]
                else:
                    print(f"Invalid selection. Please enter a number between 1 and {len(stores)}.")
            except ValueError:
                print("Invalid input. Please enter a number or 'q' to quit.")

    def crawl_pizzahut(self, zipcode: str, output_path: Optional[str] = None, mode: str = MODE_DEFAULT) -> Dict[str, Any]:
        """Crawl Pizza Hut data for a given zip code

        Args:
            zipcode: Zip code to search for
            output_path: Optional output file path
            mode: Operation mode (default, test, or show)

        Returns:
            Dictionary with the combined data or error information
        """
        # Fetch stores for the zipcode
        stores = self.fetch_stores(zipcode)

        if not stores:
            return {"error": "No stores found for the provided zip code"}

        # Handle different modes
        if mode == MODE_SHOW:
            # Show stores and let user select
            selected_store = self.select_store(stores)
            if not selected_store:
                return {"error": "Store selection cancelled"}

            store_id = selected_store.get("store_id")
            menu_data = self.fetch_menu(store_id)

            if not menu_data:
                return {"error": "Failed to fetch menu data"}

            # Create the combined data
            combined_data = self._create_combined_data(zipcode, selected_store, menu_data)

            # Save the data if output path is provided
            if output_path:
                self.save_combined_data(zipcode, selected_store, menu_data, output_path)

            # Save to database
            db_result = save_pizzahut_data_to_db(combined_data)
            if db_result.get("success"):
                print(f"✅ {db_result.get('message')}")
            else:
                print(f"⚠️ {db_result.get('message')}")

            return combined_data

        elif mode == MODE_TEST:
            # Get data for two stores (or as many as available, up to two)
            test_stores = stores[:min(2, len(stores))]
            combined_results = []

            for store in test_stores:
                store_id = store.get("store_id")
                menu_data = self.fetch_menu(store_id)

                if menu_data:
                    combined_results.append(self._create_combined_data(zipcode, store, menu_data))

            if not combined_results:
                return {"error": "Failed to fetch menu data for any store"}

            # Save the combined results if output path is provided
            if output_path:
                filename = output_path
            else:
                filename = TEST_COMBINED_FILE_TEMPLATE.format(zipcode=zipcode)

            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(combined_results, f, indent=4)
                print(f"✅ Test data for {len(combined_results)} stores saved to {os.path.abspath(filename)}")
            except Exception as e:
                print(f"❌ Failed to save test data: {str(e)}")

            return combined_results[0] if len(combined_results) == 1 else {"stores": combined_results}

        else:  # Default mode
            # Process all stores
            combined_results = []

            for idx, store in enumerate(stores):
                store_id = store.get("store_id")

                # Fetch menu data for the store
                menu_data = self.fetch_menu(store_id)

                if menu_data:
                    # Create the combined data with sequence information
                    combined_data = self._create_combined_data(zipcode, store, menu_data, sequence=idx)
                    combined_results.append(combined_data)

                    # Save to database
                    db_result = save_pizzahut_data_to_db(combined_data)
                    if db_result.get("success"):
                        print(f"✅ {db_result.get('message')}")
                    else:
                        print(f"⚠️ {db_result.get('message')}")

            if not combined_results:
                return {"error": "Failed to fetch menu data for any store"}

            # Save the combined results if output path is provided
            if output_path:
                filename = output_path
                # Create directory if it doesn't exist
                output_dir = os.path.dirname(output_path)
                if output_dir:
                    os.makedirs(output_dir, exist_ok=True)

                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(combined_results, f, indent=4)
                    print(f"✅ Data for {len(combined_results)} stores saved to {os.path.abspath(filename)}")
                except Exception as e:
                    print(f"❌ Failed to save data: {str(e)}")

            # Return all results
            return {"stores": combined_results}


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Pizza Hut Store and Menu Fetcher")
    parser.add_argument("zipcode", help="Zipcode to search for Pizza Hut stores")
    parser.add_argument("--output", help="Output file path (optional)")
    parser.add_argument("--mode", choices=[MODE_DEFAULT, MODE_TEST, MODE_SHOW], default=MODE_DEFAULT,
                        help=f"Operation mode: {MODE_DEFAULT} (automatic), {MODE_TEST} (fetch two stores), or {MODE_SHOW} (select store)")
    return parser.parse_args()


def main():
    # Parse command line arguments
    args = parse_arguments()
    zipcode = args.zipcode
    output = args.output
    mode = args.mode

    # Create API client and run the crawler
    api = PizzaHutAPI()
    result = api.crawl_pizzahut(zipcode, output, mode)

    # Check for errors
    if isinstance(result, dict) and "error" in result:
        print(f"❌ {result['error']}")
        return

    print("✅ Successfully fetched Pizza Hut data")


if __name__ == "__main__":
    main()
