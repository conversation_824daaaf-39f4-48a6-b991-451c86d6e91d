"""
Database utility functions for crawlers to save data to the database.
"""
import os
import sys
from typing import Dict, Any
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Add the parent directory to sys.path to import from db
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from db.schemas import StoreCreate, ItemCreate
from db.repositories.store_repository import StoreRepository
from db.repositories.item_repository import ItemRepository

# Load environment variables
load_dotenv()

# Get database URL from environment variables or use default
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/menu_db")

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db_session():
    """Get a database session"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise e


def save_pizzahut_data_to_db(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Save Pizza Hut data to the database

    Args:
        data: Pizza Hut data from the API

    Returns:
        Dictionary with the result information
    """
    try:
        # Create a database session
        db = get_db_session()

        try:
            # Extract store data
            # Use store_id as the restaurant_number
            restaurant_number = str(data.get("store_id", ""))

            # Check for postal code in various possible field names
            api_postal_code = (
                data.get("postal_code", "") or
                data.get("postalCode", "") or
                data.get("zip_code", "") or
                data.get("zipCode", "") or
                data.get("zip", "")
            )
            # If postal_code is not available in any form in the API response, use the searched_code (zipcode)
            postal_code = api_postal_code if api_postal_code else data.get("zipcode", "")

            # Process the postal_code to simplify it if it's in the format "90123-1234"
            if postal_code and "-" in postal_code:
                # Split by hyphen and take only the first part
                postal_code = postal_code.split("-")[0]

            # Get the zipcode and process it if it's in the format "90123-1234"
            zipcode = data.get("zipcode", "")
            if zipcode and "-" in zipcode:
                # Split by hyphen and take only the first part
                zipcode = zipcode.split("-")[0]

            store_data = {
                "restaurant": "pizzahut",
                "restaurant_number": restaurant_number,
                "address": data.get("address1", ""),
                "city": data.get("city", ""),
                "state": data.get("state", ""),
                "searched_code": zipcode,  # The code used to search for stores
                "store_code": postal_code,  # Store's actual code - use API postal_code or fall back to zipcode
                "sequence": data.get("sequence", 0),  # Original sequence from API
                "latitude": float(data.get("latitude", 0)) if data.get("latitude") else None,
                "longitude": float(data.get("longitude", 0)) if data.get("longitude") else None
            }

            # First check if store already exists by store_code (store's actual code)
            store_code = store_data["store_code"]
            searched_code = store_data["searched_code"]
            new_sequence = store_data["sequence"]

            existing_store = StoreRepository.get_by_restaurant_and_store_code(
                db,
                "pizzahut",
                store_code
            )

            # If not found by postal_code, check by restaurant_number
            if not existing_store:
                existing_store = StoreRepository.get_by_restaurant_and_restaurant_number(
                    db,
                    "pizzahut",
                    restaurant_number
                )

            if existing_store:
                # Only update if the new sequence is lower (store is more relevant)
                # or if the searched_code matches the current search
                if new_sequence < existing_store.sequence or existing_store.searched_code != searched_code:
                    # Update the store data
                    store = StoreRepository.update(db, existing_store.id, store_data)
                    # Delete existing items to refresh them
                    ItemRepository.delete_by_store_id(db, store.id)
                    print(f"✅ Updated existing Pizza Hut store: {store.id} (improved sequence or new search code)")
                else:
                    # Keep the existing store data but still use it for items
                    store = existing_store
                    print(f"ℹ️ Using existing Pizza Hut store: {store.id} (better or equal sequence)")
            else:
                # Create new store
                store_schema = StoreCreate(**store_data)
                store = StoreRepository.create(db, store_schema)
                print(f"✅ Created new Pizza Hut store: {store.id}")

            # Process and save items
            items = []

            # Get current timestamp for crawled_at
            current_time = datetime.now()

            # Process products
            for product in data.get("products", []):
                item_data = {
                    "name": product.get("name", ""),
                    "price": float(product.get("price", 0)),
                    "display_price": product.get("displayPrice", f"${float(product.get('price', 0)):.2f}"),
                    "description": product.get("description", ""),
                    "images": product.get("images", ""),
                    "item_category": product.get("itemCategory", "product"),
                    "item_type": product.get("itemType", ""),
                    "crawled_at": current_time
                }
                items.append(ItemCreate(**item_data))

            # Save items to database
            if items:
                ItemRepository.create_many(db, items, store.id)
                print(f"✅ Saved {len(items)} Pizza Hut items to database")

                # Manage crawl history - keep only the 3 most recent crawls
                deleted_count = ItemRepository.manage_crawl_history(db, store.id, 3)
                if deleted_count > 0:
                    print(f"🧹 Cleaned up {deleted_count} old Pizza Hut items")

            return {
                "success": True,
                "store_id": store.id,
                "item_count": len(items),
                "message": f"Saved Pizza Hut data to database (Store ID: {store.id}, Items: {len(items)})"
            }

        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Failed to save Pizza Hut data to database: {str(e)}"
        }


def save_chipotle_data_to_db(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Save Chipotle data to the database

    Args:
        data: Chipotle data from the API

    Returns:
        Dictionary with the result information
    """
    try:
        # Create a database session
        db = get_db_session()

        try:
            # Extract store data
            # For Chipotle, use the restaurant_number
            restaurant_number = str(data.get("restaurant_number", ""))
            # Check for postal code in various possible field names
            api_postal_code = (
                data.get("postal_code", "") or
                data.get("postalCode", "") or
                data.get("zip_code", "") or
                data.get("zipCode", "") or
                data.get("zip", "")
            )
            # If postal_code is not available in any form in the API response, use the searched_code (zipcode)
            postal_code = api_postal_code if api_postal_code else data.get("zipcode", "")

            # Process the postal_code to simplify it if it's in the format "90123-1234"
            if postal_code and "-" in postal_code:
                # Split by hyphen and take only the first part
                postal_code = postal_code.split("-")[0]

            # Get the zipcode and process it if it's in the format "90123-1234"
            zipcode = data.get("zipcode", "")
            if zipcode and "-" in zipcode:
                # Split by hyphen and take only the first part
                zipcode = zipcode.split("-")[0]

            store_data = {
                "restaurant": "chipotle",
                "restaurant_number": restaurant_number,
                "address": data.get("address1", ""),
                "city": data.get("city", ""),
                "state": data.get("state", ""),
                "searched_code": zipcode,  # The code used to search for stores
                "store_code": postal_code,  # Store's actual code - use API postal_code or fall back to zipcode
                "sequence": data.get("sequence", 0),  # Original sequence from API
                "latitude": float(data.get("latitude", 0)) if data.get("latitude") else None,
                "longitude": float(data.get("longitude", 0)) if data.get("longitude") else None
            }

            # First check if store already exists by store_code (store's actual code)
            store_code = store_data["store_code"]
            searched_code = store_data["searched_code"]
            new_sequence = store_data["sequence"]

            existing_store = StoreRepository.get_by_restaurant_and_store_code(
                db,
                "chipotle",
                store_code
            )

            # If not found by postal_code, check by restaurant_number
            if not existing_store:
                existing_store = StoreRepository.get_by_restaurant_and_restaurant_number(
                    db,
                    "chipotle",
                    restaurant_number
                )

            if existing_store:
                # Only update if the new sequence is lower (store is more relevant)
                # or if the searched_code matches the current search
                if new_sequence < existing_store.sequence or existing_store.searched_code != searched_code:
                    # Update the store data
                    store = StoreRepository.update(db, existing_store.id, store_data)
                    # Delete existing items to refresh them
                    ItemRepository.delete_by_store_id(db, store.id)
                    print(f"✅ Updated existing Chipotle store: {store.id} (improved sequence or new search code)")
                else:
                    # Keep the existing store data but still use it for items
                    store = existing_store
                    print(f"ℹ️ Using existing Chipotle store: {store.id} (better or equal sequence)")
            else:
                # Create new store
                store_schema = StoreCreate(**store_data)
                store = StoreRepository.create(db, store_schema)
                print(f"✅ Created new Chipotle store: {store.id}")

            # Process and save items
            items = []

            # Get current timestamp for crawled_at
            current_time = datetime.now()

            # Process products
            for product in data.get("products", []):
                item_data = {
                    "name": product.get("name", ""),
                    "price": float(product.get("price", 0)),
                    "display_price": product.get("displayPrice", f"${float(product.get('price', 0)):.2f}"),
                    "description": product.get("description", ""),
                    "images": product.get("images", ""),
                    "item_category": product.get("itemCategory", "product"),
                    "item_type": product.get("itemType", ""),
                    "crawled_at": current_time
                }
                items.append(ItemCreate(**item_data))

            # Save items to database
            if items:
                ItemRepository.create_many(db, items, store.id)
                print(f"✅ Saved {len(items)} Chipotle items to database")

                # Manage crawl history - keep only the 3 most recent crawls
                deleted_count = ItemRepository.manage_crawl_history(db, store.id, 3)
                if deleted_count > 0:
                    print(f"🧹 Cleaned up {deleted_count} old Chipotle items")

            return {
                "success": True,
                "store_id": store.id,
                "item_count": len(items),
                "message": f"Saved Chipotle data to database (Store ID: {store.id}, Items: {len(items)})"
            }

        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Failed to save Chipotle data to database: {str(e)}"
        }
