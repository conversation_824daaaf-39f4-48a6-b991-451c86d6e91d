# Alembic Database Migration Guide

This project uses Alembic for database schema management. All custom database setup scripts have been removed in favor of using Alembic's built-in commands.

## Prerequisites

1. **PostgreSQL Database**: Start your PostgreSQL database
   ```bash
   docker-compose up -d
   ```

2. **Virtual Environment**: Make sure your Python virtual environment is activated
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Environment Variables**: Ensure your `.env` file has the correct `DATABASE_URL`
   ```
   DATABASE_URL=postgresql://postgres:postgres@localhost:5432/menu_db
   ```

## Basic Alembic Commands

### 1. Check Current Migration Status
```bash
alembic current
```

### 2. Create a New Migration (Auto-generate from model changes)
```bash
alembic revision --autogenerate -m "description of changes"
```

### 3. Apply All Pending Migrations
```bash
alembic upgrade head
```

### 4. Downgrade One Migration
```bash
alembic downgrade -1
```

### 5. Show Migration History
```bash
alembic history
```

### 6. Show Pending Migrations
```bash
alembic show head
```

## Initial Setup (Fresh Database)

If you're starting with a fresh database:

1. **Start PostgreSQL**:
   ```bash
   docker-compose up -d
   ```

2. **Create your first migration**:
   ```bash
   alembic revision --autogenerate -m "initial migration"
   ```

3. **Apply the migration**:
   ```bash
   alembic upgrade head
   ```

## Important Notes

- **Always review auto-generated migrations** before applying them
- **Test both upgrade and downgrade** operations in development
- **Never edit applied migrations** - create new ones instead
- **The database URL is automatically loaded** from your `.env` file
- **Models are located in** `src/db/models.py`

## Configuration Files

- `alembic.ini` - Main Alembic configuration
- `src/db/migrations/env.py` - Environment setup for migrations
- `src/db/migrations/README` - Quick reference for commands
- `src/db/migrations/versions/` - Migration files (auto-generated)

## Troubleshooting

### "No such revision" error
This means no migrations have been applied yet. Create and apply an initial migration.

### Import errors
Make sure your virtual environment is activated and all dependencies are installed.

### Database connection errors
Ensure PostgreSQL is running and the DATABASE_URL in your .env file is correct.

### Multiple heads
If you have multiple migration heads, you may need to merge them:
```bash
alembic merge heads -m "merge migrations"
```

## Model Changes Workflow

1. **Modify your models** in `src/db/models.py`
2. **Generate migration**:
   ```bash
   alembic revision --autogenerate -m "describe your changes"
   ```
3. **Review the generated migration** in `src/db/migrations/versions/`
4. **Apply the migration**:
   ```bash
   alembic upgrade head
   ```
5. **Test the changes** in your application

## Clean Database Management

This setup uses only Alembic's built-in commands. No custom database setup scripts are needed or included.
