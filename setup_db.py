import os
import sys
import subprocess
import time
import psycopg2
from dotenv import load_dotenv
from sqlalchemy import inspect, create_engine
from sqlalchemy.exc import SQLAlchemyError

# Add the current directory to sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Get database URL from environment variables
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/menu_db")

# Extract connection parameters from DATABASE_URL
# Format: postgresql://user:password@host:port/dbname
parts = DATABASE_URL.split("//")[1].split("@")
user_pass = parts[0].split(":")
host_port_db = parts[1].split("/")
host_port = host_port_db[0].split(":")

DB_USER = user_pass[0]
DB_PASSWORD = user_pass[1]
DB_HOST = host_port[0]
DB_PORT = int(host_port[1]) if len(host_port) > 1 else 5432
DB_NAME = host_port_db[1]

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)

def create_database():
    """Create the database if it doesn't exist"""
    try:
        # Connect to default 'postgres' database to check if our database exists
        conn = psycopg2.connect(
            dbname="postgres",
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT
        )
        conn.autocommit = True
        cursor = conn.cursor()

        # Check if database exists
        cursor.execute(f"SELECT 1 FROM pg_catalog.pg_database WHERE datname = '{DB_NAME}'")
        exists = cursor.fetchone()

        if not exists:
            print(f"🔧 Creating database '{DB_NAME}'...")
            cursor.execute(f"CREATE DATABASE {DB_NAME}")
            print(f"✅ Database '{DB_NAME}' created successfully!")
        else:
            print(f"✅ Database '{DB_NAME}' already exists.")

        cursor.close()
        conn.close()
        return True
    except psycopg2.Error as e:
        print(f"❌ Failed to create database: {e}")
        return False

def wait_for_postgres(max_retries=10, retry_interval=2):
    """Wait for PostgreSQL to be available"""
    retries = 0

    while retries < max_retries:
        try:
            # Try to connect to the default postgres database
            conn = psycopg2.connect(
                dbname="postgres",
                user=DB_USER,
                password=DB_PASSWORD,
                host=DB_HOST,
                port=DB_PORT
            )
            conn.close()
            print("✅ PostgreSQL server is available!")
            return True
        except psycopg2.OperationalError:
            retries += 1
            print(f"⏳ Waiting for PostgreSQL to be available... ({retries}/{max_retries})")
            time.sleep(retry_interval)

    print("❌ Failed to connect to PostgreSQL")
    return False

def run_migrations():
    """Run Alembic migrations"""
    try:
        print("🔄 Running database migrations...")
        # Use 'heads' instead of 'head' to upgrade to all head revisions
        subprocess.run(["alembic", "upgrade", "heads"], check=True)
        print("✅ Migrations completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Migration failed: {e}")
        return False

def check_database_connection():
    """Check if we can connect to the database"""
    try:
        conn = psycopg2.connect(
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT
        )
        conn.close()
        print(f"✅ Successfully connected to database '{DB_NAME}'")
        return True
    except psycopg2.OperationalError:
        print(f"❌ Could not connect to database '{DB_NAME}'")
        return False

def check_tables_exist():
    """Check if required tables exist in the database"""
    try:
        # Import models here to avoid circular imports
        from db.models import Base

        # Get the inspector
        inspector = inspect(engine)

        # Get all table names from the database
        existing_tables = inspector.get_table_names()

        # Get all table names from our models
        model_tables = [table.__tablename__ for table in Base.__subclasses__()]

        # Check if all model tables exist in the database
        missing_tables = [table for table in model_tables if table not in existing_tables]

        if missing_tables:
            print(f"⚠️ Missing tables: {', '.join(missing_tables)}")
            return False
        else:
            print("✅ All required tables exist in the database")
            return True
    except SQLAlchemyError as e:
        print(f"❌ Error checking tables: {e}")
        return False

def check_table_structure():
    """Check if the table structure matches our models"""
    try:
        # Get the inspector
        inspector = inspect(engine)

        # Check if the main tables exist
        if 'stores' in inspector.get_table_names() and 'items' in inspector.get_table_names():
            # Check if the tables have the expected columns
            store_columns = [col['name'] for col in inspector.get_columns('stores')]
            item_columns = [col['name'] for col in inspector.get_columns('items')]

            # Define the expected columns
            expected_store_columns = ['id', 'restaurant', 'restaurant_number',
                                     'address', 'city', 'state', 'searched_code', 'store_code', 'sequence',
                                     'latitude', 'longitude', 'created_at', 'updated_at']
            expected_item_columns = ['id', 'store_id', 'name', 'price', 'display_price',
                                    'description', 'images', 'item_category', 'item_type',
                                    'crawled_at', 'created_at', 'updated_at']

            # Check if all expected columns exist
            missing_store_columns = [col for col in expected_store_columns if col not in store_columns]
            missing_item_columns = [col for col in expected_item_columns if col not in item_columns]

            if missing_store_columns or missing_item_columns:
                if missing_store_columns:
                    print(f"⚠️ Missing columns in 'stores' table: {', '.join(missing_store_columns)}")
                if missing_item_columns:
                    print(f"⚠️ Missing columns in 'items' table: {', '.join(missing_item_columns)}")
                return False

            print("✅ Table structure matches the expected schema")
            return True
        else:
            return False
    except SQLAlchemyError as e:
        print(f"❌ Error checking table structure: {e}")
        return False

def create_tables():
    """Create all tables defined in the models"""
    try:
        # Import models here to avoid circular imports
        from db.models import Base

        print("🔧 Creating database tables...")
        Base.metadata.create_all(engine)
        print("✅ Database tables created successfully!")
        return True
    except SQLAlchemyError as e:
        print(f"❌ Failed to create tables: {e}")
        return False

def run_migrations_with_stamp():
    """Run Alembic migrations with stamp to mark current revision"""
    try:
        print("🔄 Stamping current database state...")
        # First stamp the current state to avoid conflicts
        subprocess.run(["alembic", "stamp", "head"], check=True)
        print("✅ Database state stamped successfully!")

        # Then run the migrations
        print("🔄 Running database migrations...")
        subprocess.run(["alembic", "upgrade", "heads"], check=True)
        print("✅ Migrations completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Migration operation failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Setting up the database...")

    # First, wait for PostgreSQL server to be available
    if not wait_for_postgres():
        print("❌ Cannot proceed without PostgreSQL connection")
        sys.exit(1)

    # Then create the database if it doesn't exist
    if not create_database():
        print("❌ Cannot proceed without database")
        sys.exit(1)

    # Check if we can connect to the database
    if not check_database_connection():
        print("❌ Cannot proceed without database connection")
        sys.exit(1)

    # Check if tables exist and have the correct structure
    tables_exist = check_tables_exist()

    if tables_exist:
        # If tables exist, check their structure
        if check_table_structure():
            print("✅ Database is fully set up and ready to use")
        else:
            print("⚠️ Tables exist but structure may not match. Attempting to fix...")
            # Try to stamp and run migrations to fix structure
            if run_migrations_with_stamp():
                print("✅ Database structure updated successfully")
            else:
                print("⚠️ Could not update database structure automatically")
                print("ℹ️ The database can still be used, but some features may not work correctly")
    else:
        print("⚠️ Some tables are missing. Attempting to create them...")

        # Try running migrations first (preferred method)
        if run_migrations():
            print("✅ Tables created through migrations")
        else:
            # If migrations fail, try direct table creation
            print("⚠️ Migrations failed, trying direct table creation...")
            if create_tables():
                print("✅ Tables created directly")
            else:
                print("❌ Failed to create tables")
                sys.exit(1)

    print("🎉 Database setup complete! You can now use the application.")
