# Restaurant Menu API

A Python-based API for extracting and storing menu data from various restaurant chains including Pizza Hut and Chipotle.

## Features

- Scrapes menu items and prices based on zip code
- Stores data about restaurant locations by zip code
- Provides a FastAPI endpoint for accessing menu data
- Stores data in PostgreSQL database
- Handles dynamic content loading and store selection
- Saves results in JSON format

## Requirements

- Python 3.12.9
- Chrome browser (for web scraping)
- ChromeDriver (automatically managed)
- PostgreSQL (Docker setup provided)
- Docker and Docker Compose (optional, for database setup)

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd MenuScrapers
```

2. Create and activate a virtual environment (recommended):

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install required packages:

```bash
pip install -r requirements.txt
```

## Usage

### Database Setup

#### One-Click Setup (Windows)

Simply double-click the `setup_db.bat` file to automatically:

- Check PostgreSQL connection
- Create the database if it doesn't exist
- Create all required tables if they don't exist

#### Using Docker (Recommended)

```bash
# Start PostgreSQL container
docker-compose up -d

# Run database setup script
python setup_db.py
```

#### Using Existing PostgreSQL

1. Update the `.env` file with your PostgreSQL connection details:

   ```
   DATABASE_URL=postgresql://username:password@localhost:5432/menu_db
   ```

2. Run the setup script:
   ```bash
   python setup_db.py
   ```

The setup script will:

1. Check if the database exists and create it if needed
2. Verify all required tables exist
3. Create missing tables using migrations or direct table creation

### Running the API

```bash
# Start the FastAPI server
python -m api.main
```

The API will be available at http://localhost:8000

### API Endpoints

- `GET /menu?store_id=1` - Get menu for a specific store
- `POST /menu/fetch?restaurant=pizzahut&zipcode=90210` - Fetch menu data from the API and save it to the database
- `GET /menu/stores` - Get all stores

### Command Line (Legacy)

```bash
# Run with headless browser (default)
python src/main.py pizzahut --zip-code 90210 --output output/menu.json

# Run with visible browser
python src/main.py pizzahut --zip-code 90210 --output output/menu.json --show-browser
```

## Output Format

The scraper generates two types of data:

1. Store Information (`stores_database.json`):

```json
{
  "90210": ["1234 Beverly Hills St, Beverly Hills, CA 90210"]
}
```

2. Menu Data (`output/menu.json`):

```json
{
  "Pizza": [
    {
      "name": "Pepperoni Pizza",
      "description": "Classic pepperoni pizza with cheese",
      "price": "$12.99"
    }
  ]
}
```

## Project Structure

```
Restaurant-Menu-API/
├── api/
│   ├── __init__.py
│   ├── main.py
│   ├── routers/
│   │   ├── __init__.py
│   │   └── menu.py
│   └── services/
│       ├── __init__.py
│       └── menu_service.py
├── db/
│   ├── __init__.py
│   ├── database.py
│   ├── models.py
│   ├── schemas.py
│   ├── repositories/
│   │   ├── __init__.py
│   │   ├── store_repository.py
│   │   └── item_repository.py
│   └── migrations/
│       ├── README
│       ├── env.py
│       ├── script.py.mako
│       └── versions/
├── src/
│   ├── __init__.py
│   ├── main.py
│   ├── app.py  # Legacy Flask API (kept for reference)
│   ├── subway.py  # Experimental crawler (kept for reference)
│   ├── crawlers/
│   │   ├── db_utils.py
│   │   ├── Pizza_hut/
│   │   │   ├── __init__.py
│   │   │   ├── pizzahut_api.py
│   │   │   └── const.py
│   │   └── Chipotle/
│   │       ├── __init__.py
│   │       ├── chipotle_api.py
│   │       └── const.py
├── output/
│   └── menu.json
├── alembic.ini
├── docker-compose.yml
├── setup_db.py
├── setup_db.bat
├── .env
├── requirements.txt
├── README.md
└── .gitignore
```

## Error Handling

The scraper includes robust error handling for:

- Network issues
- Missing elements
- Dynamic content loading
- Invalid zip codes

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request
