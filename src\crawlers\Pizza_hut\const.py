

# API URLs
STORES_API_URL = "https://services.digital.pizzahut.com/phdapi/v2/stores/search?key=8CNl1ssOwYGrdFM924imkWYbXnJ5totq"
MENU_API_URL_TEMPLATE = "https://services.digital.pizzahut.com/content-gateway/stores/{store_id}/menu?apikey=TGwHGN74nGuUjhpdFAvYqcCMALAlWRDA"

# Request headers
STORES_HEADERS = {
    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
}

MENU_HEADERS = {
    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
}

# Request payload constants
OCCASION_ID = "CARRYOUT"

# File name templates
STORE_FILE_TEMPLATES = [
    "pizza_hut_stores_{zipcode}.json",
    "pizza_hut_stores_zip_{zipcode}.json"
]

COMBINED_FILE_TEMPLATE = "pizza_hut_combined_{zipcode}_{store_id}.json"

# Table headers for display
STORE_TABLE_HEADERS = ["#", "Store ID", "Store Number", "State", "City", "Street", "Distance"]

# Mode constants
MODE_TEST = "test"
MODE_SHOW = "show"
MODE_DEFAULT = "default"

# Test mode combined file template
TEST_COMBINED_FILE_TEMPLATE = "pizza_hut_test_{zipcode}.json"