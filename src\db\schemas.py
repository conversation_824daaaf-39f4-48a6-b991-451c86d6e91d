from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

# Item schemas
class ItemBase(BaseModel):
    name: str
    price: float
    display_price: str
    description: Optional[str] = None
    images: Optional[str] = None
    item_category: Optional[str] = None
    item_type: Optional[str] = None

class ItemCreate(ItemBase):
    crawled_at: datetime

class Item(ItemBase):
    id: int
    store_id: int
    crawled_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
        from_attributes = True

# Store schemas
class StoreBase(BaseModel):
    restaurant: str
    restaurant_number: str
    address: str
    city: str
    state: str
    searched_code: str  # The code used to search for stores
    store_code: str  # Store's actual code (renamed from postal_code)
    sequence: int = 0  # Original sequence from API
    latitude: Optional[float] = None
    longitude: Optional[float] = None

class StoreCreate(StoreBase):
    pass

class Store(StoreBase):
    id: int
    created_at: datetime
    updated_at: datetime
    items: List[Item] = []

    class Config:
        orm_mode = True
        from_attributes = True

# Menu response schema
class MenuResponse(BaseModel):
    store: Store
