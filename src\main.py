import argparse
import j<PERSON>
# Removed unused import
from typing import Dict, Any
# Removed old import
from crawlers.Pizza_hut import PizzaHutAP<PERSON>
from crawlers.Chipotle import <PERSON>otleAP<PERSON>


def run_pizzahut_crawler(args: argparse.Namespace) -> Dict[str, Any]:
    # Create an instance of PizzaHutAPI
    crawler = PizzaHutAPI(headless=args.headless)
    # Call the crawl_pizzahut method with the zip code, output path, and mode
    mode = getattr(args, 'mode', 'default')
    return crawler.crawl_pizzahut(args.zip_code, args.output, mode)


def run_chipotle_crawler(args: argparse.Namespace) -> Dict[str, Any]:
    # Use the new ChipotleAPI instead of the old crawler
    crawler = ChipotleAPI(headless=args.headless)
    # Call the crawl_chipotle method with the zip code, output path, and mode
    mode = getattr(args, 'mode', 'default')
    return crawler.crawl_chipotle(args.zip_code, args.output, mode)


def main():
    parser = argparse.ArgumentParser(description="Menu Crawler CLI")
    parser.add_argument("crawler", choices=["pizzahut", "chipotle"], help="Crawler to use")
    parser.add_argument("--zip-code", required=True, help="Zip code for the crawler")
    parser.add_argument("--output", help="Output file path (optional)")
    parser.add_argument("--show-browser", action="store_true", help="Show browser window while crawling")
    parser.add_argument("--headless", action="store_true", default=True, help="Run browser in headless mode")
    parser.add_argument("--mode", choices=["default", "test", "show"], default="default",
                        help="Operation mode for Pizza Hut: default (automatic), test (fetch two stores), or show (select store)")

    args = parser.parse_args()

    # If show-browser is True, override headless to False
    if args.show_browser:
        args.headless = False

    crawlers = {
        "pizzahut": run_pizzahut_crawler,
        "chipotle": run_chipotle_crawler
    }

    # Run the selected crawler
    result = crawlers[args.crawler](args)

    # The API should handle saving the file if output_path is provided
    # Just print the result if no output was specified
    if not args.output:
        print(json.dumps(result, indent=2))


if __name__ == "__main__":
    main()