from sqlalchemy import Column, Integer, String, Float, Text, ForeignKey, DateTime, func
from sqlalchemy.orm import relationship
from .database import Base

class Store(Base):
    __tablename__ = "stores"

    id = Column(Integer, primary_key=True, index=True)
    restaurant = Column(String, nullable=False)
    restaurant_number = Column(String, nullable=False, index=True)
    address = Column(String, nullable=False)
    city = Column(String, nullable=False)
    state = Column(String, nullable=False)
    searched_code = Column(String, nullable=False)  # The code used to search for stores
    store_code = Column(String, nullable=False, index=True)  # Store's actual code (renamed from postal_code)
    sequence = Column(Integer, nullable=False, default=0)  # Original sequence from API
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationship with items
    items = relationship("Item", back_populates="store", cascade="all, delete-orphan")

class Item(Base):
    __tablename__ = "items"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id", ondelete="CASCADE"), nullable=False)
    name = Column(String, nullable=False)
    price = Column(Float, nullable=False)
    display_price = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    images = Column(String, nullable=True)
    item_category = Column(String, nullable=True)
    item_type = Column(String, nullable=True)
    crawled_at = Column(DateTime, nullable=False, index=True)  # When the item was crawled
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationship with store
    store = relationship("Store", back_populates="items")
