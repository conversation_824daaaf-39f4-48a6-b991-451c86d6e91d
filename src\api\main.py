from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api.routers import menu
import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="Restaurant Menu API",
    description="API for fetching and storing restaurant menu data",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Include routers
app.include_router(menu.router)

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Welcome to the Restaurant Menu API",
        "docs": "/docs",
        "endpoints": {
            "menu_by_database_id": "/menu?store_id={store_id}",
            "menu_by_restaurant_number": "/menu?restaurant_number={restaurant_number}",
            "menu_by_restaurant": "/menu?restaurant={restaurant}",
            "menu_by_restaurant_and_zipcode": "/menu?restaurant={restaurant}&zipcode={zipcode}"
        }
    }

if __name__ == "__main__":
    # Get port from environment variable or use default
    port = int(os.getenv("PORT", 8000))

    # Run the application
    uvicorn.run("api.main:app", host="0.0.0.0", port=port, reload=True)
