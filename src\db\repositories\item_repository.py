from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import List, Optional, Set
from datetime import datetime
from db.models import Item
from db.schemas import ItemCreate

class ItemRepository:
    @staticmethod
    def get_all(db: Session, skip: int = 0, limit: int = 100) -> List[Item]:
        return db.query(Item).offset(skip).limit(limit).all()

    @staticmethod
    def get_by_id(db: Session, item_id: int) -> Optional[Item]:
        return db.query(Item).filter(Item.id == item_id).first()

    @staticmethod
    def get_by_store_id(db: Session, store_id: int) -> List[Item]:
        return db.query(Item).filter(Item.store_id == store_id).all()

    @staticmethod
    def create(db: Session, item: ItemCreate, store_id: int) -> Item:
        # Use model_dump instead of dict (which is deprecated in Pydantic v2)
        db_item = Item(**item.model_dump(), store_id=store_id)
        db.add(db_item)
        db.commit()
        db.refresh(db_item)
        return db_item

    @staticmethod
    def create_many(db: Session, items: List[ItemCreate], store_id: int) -> List[Item]:
        # Use model_dump instead of dict (which is deprecated in Pydantic v2)
        db_items = [Item(**item.model_dump(), store_id=store_id) for item in items]
        db.add_all(db_items)
        db.commit()
        for item in db_items:
            db.refresh(item)
        return db_items

    @staticmethod
    def update(db: Session, item_id: int, item_data: dict) -> Optional[Item]:
        db_item = db.query(Item).filter(Item.id == item_id).first()
        if db_item:
            for key, value in item_data.items():
                setattr(db_item, key, value)
            db.commit()
            db.refresh(db_item)
        return db_item

    @staticmethod
    def delete(db: Session, item_id: int) -> bool:
        db_item = db.query(Item).filter(Item.id == item_id).first()
        if db_item:
            db.delete(db_item)
            db.commit()
            return True
        return False

    @staticmethod
    def delete_by_store_id(db: Session, store_id: int) -> int:
        items = db.query(Item).filter(Item.store_id == store_id).all()
        count = len(items)
        for item in items:
            db.delete(item)
        db.commit()
        return count

    @staticmethod
    def get_crawl_dates_for_store(db: Session, store_id: int) -> List[datetime]:
        """Get all unique crawl dates for a store, ordered by most recent first"""
        dates = db.query(Item.crawled_at).filter(
            Item.store_id == store_id
        ).distinct().order_by(desc(Item.crawled_at)).all()
        return [date[0] for date in dates]

    @staticmethod
    def delete_items_by_crawl_date(db: Session, store_id: int, crawl_date: datetime) -> int:
        """Delete all items for a store with a specific crawl date"""
        items = db.query(Item).filter(
            Item.store_id == store_id,
            Item.crawled_at == crawl_date
        ).all()
        count = len(items)
        for item in items:
            db.delete(item)
        db.commit()
        return count

    @staticmethod
    def manage_crawl_history(db: Session, store_id: int, max_crawl_dates: int = 3) -> int:
        """
        Manage crawl history for a store, keeping only the most recent max_crawl_dates

        Args:
            db: Database session
            store_id: Store ID
            max_crawl_dates: Maximum number of crawl dates to keep

        Returns:
            Number of deleted items
        """
        # Get all crawl dates for the store
        crawl_dates = ItemRepository.get_crawl_dates_for_store(db, store_id)

        # If we have more than max_crawl_dates, delete the oldest ones
        deleted_count = 0
        if len(crawl_dates) > max_crawl_dates:
            # Get the dates to delete (all except the most recent max_crawl_dates)
            dates_to_delete = crawl_dates[max_crawl_dates:]

            # Delete items for each date
            for date in dates_to_delete:
                deleted_count += ItemRepository.delete_items_by_crawl_date(db, store_id, date)

        return deleted_count
